# Python 3.13 兼容性说明

## 问题描述

在Python 3.13版本中，`threading`模块进行了一些变更，导致watchdog库无法正常工作。具体错误为：

```
TypeError: 'handle' must be a _ThreadHandle
```

这是因为Python 3.13在`threading._start_joinable_thread`函数中添加了一个新的必需参数`handle`，而watchdog库使用的是旧版本的API。

## 解决方案

我们在程序中添加了兼容性代码，通过以下方式解决这个问题：

1. 检测Python版本是否为3.13或更高
2. 如果是，则修改`threading._start_joinable_thread`函数，使其兼容watchdog库的调用方式
3. 这种方法是一种临时解决方案，直到watchdog库官方更新以支持Python 3.13

## 如何运行

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行程序

```bash
python file_monitor.py
```

按照提示输入要监控的盘符，例如：`C:,D:`

## 注意事项

1. 此修复方案是针对Python 3.13版本的临时解决方案
2. 如果您使用的是Python 3.12或更低版本，程序将正常运行，不需要特殊处理
3. 如果watchdog库在未来发布了支持Python 3.13的新版本，建议更新到该版本

## 技术细节

修复代码的核心部分如下：

```python
# 检查Python版本，处理Python 3.13+的兼容性问题
import platform
python_version = platform.python_version_tuple()
if int(python_version[0]) >= 3 and int(python_version[1]) >= 13:
    # Python 3.13+版本需要特殊处理watchdog的兼容性问题
    import threading
    # 保存原始的_start_joinable_thread函数
    original_start_joinable_thread = threading._start_joinable_thread
    
    # 定义一个兼容性包装函数
    def compatible_start_joinable_thread(bootstrap, *, daemon=False, **kwargs):
        # 移除Python 3.13+中新增的handle参数
        if 'handle' in kwargs:
            del kwargs['handle']
        return original_start_joinable_thread(bootstrap, daemon=daemon, **kwargs)
    
    # 替换原始函数
    threading._start_joinable_thread = compatible_start_joinable_thread
```

这段代码通过猴子补丁(monkey patching)的方式修改了Python标准库的行为，使其能够兼容watchdog库的调用方式。