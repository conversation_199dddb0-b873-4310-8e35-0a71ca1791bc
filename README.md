# 电脑文件监控软件

这是一个使用Python开发的电脑文件监控软件，能够监控以下内容：

- 注册表变化（特别是开机启动项）
- Windows服务状态变化
- 指定盘符下的文件变化（创建、修改、删除、移动）
- 临时文件的变化

软件提供命令行和图形用户界面(GUI)两种使用方式。

## 功能特点

- 实时监控文件系统变化
- 监控Windows注册表关键路径
- 监控系统服务的状态变化
- 特别关注临时文件夹中的文件变化
- 所有监控记录保存到日志文件中
- 提供图形用户界面，操作更加便捷
- 支持日志过滤和搜索功能

## 安装要求

- Python 3.6 或更高版本
- Windows操作系统

## 安装步骤

1. 确保已安装Python 3.6+
2. 安装所需的依赖包：

```bash
pip install -r requirements.txt
```

## 使用方法

### 命令行模式

1. 运行程序：

```bash
python file_monitor.py
```

2. 按照提示输入要监控的盘符（例如：`C:,D:`）
3. 程序将开始监控并在控制台显示变化
4. 所有监控记录也会保存到`file_monitor.log`文件中
5. 按`Ctrl+C`可以停止监控

### 图形界面模式

1. 运行GUI程序：

```bash
python file_monitor_gui.py
```

或者双击`start_monitor_gui.bat`文件启动

2. 在"监控设置"选项卡中：
   - 选择要监控的盘符
   - 选择是否监控注册表、服务和临时文件
   - 设置监控检查间隔（秒）
   - 设置日志文件路径
3. 点击"开始监控"按钮开始监控
4. 在"监控日志"选项卡中查看实时日志
   - 可以使用过滤功能筛选特定类型的日志
   - 可以使用搜索功能查找特定内容
   - 可以保存或清空日志
5. 点击"停止监控"按钮停止监控

## 监控内容详情

### 文件监控
- 监控指定盘符下所有文件的创建、修改、删除和移动操作

### 注册表监控
- 监控系统和用户级别的自启动项注册表路径

### 服务监控
- 监控Windows服务的添加、删除和状态变化

### 临时文件监控
- 监控系统临时文件夹中的文件变化

## 注意事项

- 监控大型盘符（如整个C盘）可能会产生大量日志
- 程序需要管理员权限才能监控某些系统路径
- 长时间运行可能会导致日志文件变大
- GUI模式下可以随时保存、清空和过滤日志
- 如果使用Python 3.13+版本，请参考`PYTHON_3_13_兼容性说明.md`文件