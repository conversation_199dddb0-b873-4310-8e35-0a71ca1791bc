import os
import time
import sys
import winreg
import psutil
import logging

# 常量定义
DEFAULT_LOG_FILE = "file_monitor.log"
REGISTRY_PATHS = [
    (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"),
    (winreg.HKEY_CURRENT_USER, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run")
]

# 导入必要的库
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
except ImportError:
    print("请先安装watchdog库: pip install watchdog")
    sys.exit(1)

# 检查Python版本，处理Python 3.13+的兼容性问题
import platform
python_version = platform.python_version_tuple()
if int(python_version[0]) >= 3 and int(python_version[1]) >= 13:
    # Python 3.13+版本需要特殊处理watchdog的兼容性问题
    import threading
    # 保存原始的_start_joinable_thread函数
    original_start_joinable_thread = threading._start_joinable_thread
    
    # 定义一个兼容性包装函数
    def compatible_start_joinable_thread(bootstrap, *, daemon=False, **kwargs):
        # 移除Python 3.13+中新增的handle参数
        if 'handle' in kwargs:
            del kwargs['handle']
        return original_start_joinable_thread(bootstrap, daemon=daemon, **kwargs)
    
    # 替换原始函数
    threading._start_joinable_thread = compatible_start_joinable_thread

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler(DEFAULT_LOG_FILE),
        logging.StreamHandler()
    ]
)

class FileChangeHandler(FileSystemEventHandler):
    """处理文件系统事件的类"""
    
    def on_created(self, event):
        if not event.is_directory:
            logging.info(f"文件创建: {event.src_path}")
    
    def on_deleted(self, event):
        if not event.is_directory:
            logging.info(f"文件删除: {event.src_path}")
    
    def on_modified(self, event):
        if not event.is_directory:
            logging.info(f"文件修改: {event.src_path}")
    
    def on_moved(self, event):
        if not event.is_directory:
            logging.info(f"文件移动: {event.src_path} -> {event.dest_path}")

class RegistryMonitor:
    """监控注册表变化的类"""
    
    def __init__(self, hive, subkey):
        self.hive = hive
        self.subkey = subkey
        self.last_values = self._get_registry_values()
    
    def _get_registry_values(self):
        try:
            registry_key = winreg.OpenKey(self.hive, self.subkey, 0, winreg.KEY_READ)
            values = {}
            
            try:
                i = 0
                while True:
                    name, value, type_id = winreg.EnumValue(registry_key, i)
                    values[name] = (value, type_id)
                    i += 1
            except OSError:
                # 没有更多的值
                pass

            try:
                i = 0
                while True:
                    subkey_name = winreg.EnumKey(registry_key, i)
                    values[f"SUBKEY_{subkey_name}"] = "SUBKEY"
                    i += 1
            except OSError:
                # 没有更多的子键
                pass
                
            winreg.CloseKey(registry_key)
            return values
        except Exception as e:
            logging.error(f"读取注册表错误: {e}")
            return {}
    
    def check_changes(self):
        current_values = self._get_registry_values()
        
        # 检查新增或修改的值
        for name, (value, type_id) in current_values.items():
            if name not in self.last_values:
                logging.info(f"注册表新增: {self.subkey}\\{name} = {value}")
            elif self.last_values[name] != (value, type_id) and name.startswith("SUBKEY_") is False:
                logging.info(f"注册表修改: {self.subkey}\\{name} = {value} (原值: {self.last_values[name][0]})")
        
        # 检查删除的值
        for name in self.last_values:
            if name not in current_values:
                logging.info(f"注册表删除: {self.subkey}\\{name}")
        
        self.last_values = current_values

class ServiceMonitor:
    """监控Windows服务的类"""
    
    def __init__(self):
        self.last_services = self._get_services()
    
    def _get_services(self):
        services = {}
        for service in psutil.win_service_iter():
            try:
                service_info = service.as_dict()
                services[service_info['name']] = service_info['status']
            except Exception:
                pass
        return services
    
    def check_changes(self):
        current_services = self._get_services()
        
        # 检查新增或状态变化的服务
        for name, status in current_services.items():
            if name not in self.last_services:
                logging.info(f"服务新增: {name} (状态: {status})")
            elif self.last_services[name] != status:
                logging.info(f"服务状态变化: {name} (新状态: {status}, 原状态: {self.last_services[name]})")
        
        # 检查删除的服务
        for name in self.last_services:
            if name not in current_services:
                logging.info(f"服务删除: {name}")
        
        self.last_services = current_services

class TempFileMonitor:
    """专门监控临时文件的类"""
    
    def __init__(self):
        # 获取系统临时文件夹路径
        self.temp_dirs = [
            os.environ.get('TEMP'),
            os.environ.get('TMP'),
            'C:\\Windows\\Temp'
        ]
        self.last_files = self._get_temp_files()
    
    def _get_temp_files(self):
        temp_files = {}
        for temp_dir in self.temp_dirs:
            if temp_dir and os.path.exists(temp_dir):
                for root, _, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            temp_files[file_path] = os.path.getmtime(file_path)
                        except:
                            pass
        return temp_files
    
    def check_changes(self):
        current_files = self._get_temp_files()
        
        # 检查新增的临时文件
        for file_path, mtime in current_files.items():
            if file_path not in self.last_files:
                logging.info(f"临时文件创建: {file_path}")
            elif self.last_files[file_path] != mtime:
                logging.info(f"临时文件修改: {file_path}")
        
        # 检查删除的临时文件
        for file_path in self.last_files:
            if file_path not in current_files:
                logging.info(f"临时文件删除: {file_path}")
        
        self.last_files = current_files

def monitor_drive(drive_path):
    """监控指定盘符下的文件变化"""
    event_handler = FileChangeHandler()
    observer = Observer()
    observer.schedule(event_handler, drive_path, recursive=True)
    observer.start()
    return observer

def main():
    print("=== 文件监控系统启动 ===")
    print("监控内容: 注册表、服务、指定盘符文件变化、临时文件")
    print("日志将保存到 file_monitor.log 文件中")
    
    # 获取要监控的盘符
    drives_to_monitor = []
    while not drives_to_monitor:
        drive_input = input("请输入要监控的盘符(多个盘符用逗号分隔，例如 C:,D:): ")
        drives = [d.strip() for d in drive_input.split(',')]
        
        for drive in drives:
            if os.path.exists(drive):
                drives_to_monitor.append(drive)
            else:
                print(f"盘符 {drive} 不存在，已忽略")
    
    # 获取要监控的注册表路径
    registry_paths = [
        (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"),
        (winreg.HKEY_CURRENT_USER, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run")
    ]
    
    # 创建监控器
    observers = [monitor_drive(drive) for drive in drives_to_monitor]
    registry_monitors = [RegistryMonitor(hive, subkey) for hive, subkey in registry_paths]
    service_monitor = ServiceMonitor()
    temp_file_monitor = TempFileMonitor()
    
    try:
        while True:
            # 检查注册表变化
            for monitor in registry_monitors:
                monitor.check_changes()
            
            # 检查服务变化
            service_monitor.check_changes()
            
            # 检查临时文件变化
            temp_file_monitor.check_changes()
            
            # 每5秒检查一次
            time.sleep(5)
    except KeyboardInterrupt:
        print("\n正在停止监控...")
        for observer in observers:
            observer.stop()
        
        for observer in observers:
            observer.join()
        
        print("监控已停止")

if __name__ == "__main__":
    main()