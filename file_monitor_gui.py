import os
import time
import sys
import winreg
import win32serviceutil
import win32service
import win32con
import win32api
import win32evtlog
import psutil
import logging
import threading
import configparser
import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox
from datetime import datetime

# 导入必要的库
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
except ImportError:
    print("请先安装watchdog库: pip install watchdog")
    sys.exit(1)

# 检查Python版本，处理Python 3.13+的兼容性问题
import platform
python_version = platform.python_version_tuple()
if int(python_version[0]) >= 3 and int(python_version[1]) >= 13:
    # Python 3.13+版本需要特殊处理watchdog的兼容性问题
    import threading
    # 保存原始的_start_joinable_thread函数
    original_start_joinable_thread = threading._start_joinable_thread
    
    # 定义一个兼容性包装函数
    def compatible_start_joinable_thread(bootstrap, *, daemon=False, **kwargs):
        # 移除Python 3.13+中新增的handle参数
        if 'handle' in kwargs:
            del kwargs['handle']
        return original_start_joinable_thread(bootstrap, daemon=daemon, **kwargs)
    
    # 替换原始函数
    threading._start_joinable_thread = compatible_start_joinable_thread

# 自定义日志处理器，将日志发送到GUI
class GUILogHandler(logging.Handler):
    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        self.text_widget = text_widget
        self.setFormatter(logging.Formatter('%(asctime)s - %(message)s', '%Y-%m-%d %H:%M:%S'))

    def emit(self, record):
        msg = self.format(record)
        def append():
            # 更新日志文本框
            self.text_widget.configure(state='normal')
            self.text_widget.insert(tk.END, msg + '\n')
            self.text_widget.see(tk.END)
            self.text_widget.configure(state='disabled')

        # 在主线程中更新GUI
        self.text_widget.after(0, append)

class FileChangeHandler(FileSystemEventHandler):
    """处理文件系统事件的类"""
    
    def __init__(self, excluded_paths=None):
        super().__init__()
        self.excluded_paths = excluded_paths or []
    
    def _is_excluded(self, path):
        """检查路径是否在排除列表中"""
        for excluded_path in self.excluded_paths:
            # 检查路径是否以排除路径开头
            if os.path.normpath(path).startswith(os.path.normpath(excluded_path)):
                return True
        return False
    
    def on_created(self, event):
        if not event.is_directory and not self._is_excluded(event.src_path):
            logging.info(f"文件创建: {event.src_path}")
    
    def on_deleted(self, event):
        if not event.is_directory and not self._is_excluded(event.src_path):
            logging.info(f"文件删除: {event.src_path}")
    
    def on_modified(self, event):
        if not event.is_directory and not self._is_excluded(event.src_path):
            logging.info(f"文件修改: {event.src_path}")
    
    def on_moved(self, event):
        if not event.is_directory and not self._is_excluded(event.src_path) and not self._is_excluded(event.dest_path):
            logging.info(f"文件移动: {event.src_path} -> {event.dest_path}")

class RegistryMonitor:
    """监控注册表变化的类"""
    
    def __init__(self, hive, subkey):
        self.hive = hive
        self.subkey = subkey
        self.last_values = self._get_registry_values()
    
    def _get_registry_values(self):
        try:
            registry_key = winreg.OpenKey(self.hive, self.subkey, 0, winreg.KEY_READ)
            values = {}
            
            try:
                i = 0
                while True:
                    name, value, type_id = winreg.EnumValue(registry_key, i)
                    values[name] = (value, type_id)
                    i += 1
            except WindowsError:
                # 没有更多的值
                pass
                
            try:
                i = 0
                while True:
                    subkey_name = winreg.EnumKey(registry_key, i)
                    values[f"SUBKEY_{subkey_name}"] = "SUBKEY"
                    i += 1
            except WindowsError:
                # 没有更多的子键
                pass
                
            winreg.CloseKey(registry_key)
            return values
        except Exception as e:
            logging.error(f"读取注册表错误: {e}")
            return {}
    
    def check_changes(self):
        current_values = self._get_registry_values()
        
        # 检查新增或修改的值
        for name, (value, type_id) in current_values.items():
            if name not in self.last_values:
                logging.info(f"注册表新增: {self.subkey}\\{name} = {value}")
            elif self.last_values[name] != (value, type_id) and name.startswith("SUBKEY_") is False:
                logging.info(f"注册表修改: {self.subkey}\\{name} = {value} (原值: {self.last_values[name][0]})")
        
        # 检查删除的值
        for name in self.last_values:
            if name not in current_values:
                logging.info(f"注册表删除: {self.subkey}\\{name}")
        
        self.last_values = current_values

class ServiceMonitor:
    """监控Windows服务的类"""
    
    def __init__(self):
        self.last_services = self._get_services()
    
    def _get_services(self):
        services = {}
        for service in psutil.win_service_iter():
            try:
                service_info = service.as_dict()
                services[service_info['name']] = service_info['status']
            except Exception:
                pass
        return services
    
    def check_changes(self):
        current_services = self._get_services()
        
        # 检查新增或状态变化的服务
        for name, status in current_services.items():
            if name not in self.last_services:
                logging.info(f"服务新增: {name} (状态: {status})")
            elif self.last_services[name] != status:
                logging.info(f"服务状态变化: {name} (新状态: {status}, 原状态: {self.last_services[name]})")
        
        # 检查删除的服务
        for name in self.last_services:
            if name not in current_services:
                logging.info(f"服务删除: {name}")
        
        self.last_services = current_services

class TempFileMonitor:
    """专门监控临时文件的类"""
    
    def __init__(self):
        # 获取系统临时文件夹路径
        self.temp_dirs = [
            os.environ.get('TEMP'),
            os.environ.get('TMP'),
            'C:\\Windows\\Temp'
        ]
        self.last_files = self._get_temp_files()
    
    def _get_temp_files(self):
        temp_files = {}
        for temp_dir in self.temp_dirs:
            if temp_dir and os.path.exists(temp_dir):
                for root, _, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            temp_files[file_path] = os.path.getmtime(file_path)
                        except:
                            pass
        return temp_files
    
    def check_changes(self):
        current_files = self._get_temp_files()
        
        # 检查新增的临时文件
        for file_path, mtime in current_files.items():
            if file_path not in self.last_files:
                logging.info(f"临时文件创建: {file_path}")
            elif self.last_files[file_path] != mtime:
                logging.info(f"临时文件修改: {file_path}")
        
        # 检查删除的临时文件
        for file_path in self.last_files:
            if file_path not in current_files:
                logging.info(f"临时文件删除: {file_path}")
        
        self.last_files = current_files

class FileMonitorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("文件监控系统")
        self.root.geometry("900x700")  # 增加窗口高度
        self.root.minsize(800, 600)  # 增加最小高度
        
        # 设置应用图标
        try:
            self.root.iconbitmap("monitor.ico")
        except:
            pass
        
        # 配置文件路径
        self.config_file = "config.ini"
        
        # 创建主框架
        self.main_frame = ttk.Frame(root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建选项卡
        self.tab_control = ttk.Notebook(self.main_frame)
        
        # 监控设置选项卡
        self.settings_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.settings_tab, text="监控设置")
        
        # 监控日志选项卡
        self.log_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.log_tab, text="监控日志")
        
        # 关于选项卡
        self.about_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.about_tab, text="关于")
        
        self.tab_control.pack(fill=tk.BOTH, expand=True)
        
        # 设置选项卡内容
        self._setup_settings_tab()
        self._setup_log_tab()
        self._setup_about_tab()
        
        # 初始化监控状态变量
        self.is_monitoring = False
        self.monitor_thread = None
        self.stop_event = threading.Event()
        
        # 初始化监控对象
        self.observers = []
        self.registry_monitors = []
        self.service_monitor = None
        self.temp_file_monitor = None
        self.monitor_paths = []
        self.excluded_paths = []
        
        # 加载配置
        self.load_config()
        
        # 配置日志
        self._setup_logging()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def _setup_settings_tab(self):
        # 创建设置框架
        settings_frame = ttk.LabelFrame(self.settings_tab, text="监控设置")
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 监控选择区域
        monitor_selection_frame = ttk.Frame(settings_frame)
        monitor_selection_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 注册表监控复选框
        self.registry_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(monitor_selection_frame, text="注册表监控", variable=self.registry_var).pack(side=tk.LEFT, padx=10)
        
        # 服务监控复选框
        self.service_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(monitor_selection_frame, text="服务监控", variable=self.service_var).pack(side=tk.LEFT, padx=10)
        
        # 文件系统监控复选框
        self.file_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(monitor_selection_frame, text="文件系统监控", variable=self.file_var).pack(side=tk.LEFT, padx=10)
        
        # 临时文件监控复选框
        self.temp_file_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(monitor_selection_frame, text="临时文件监控", variable=self.temp_file_var).pack(side=tk.LEFT, padx=10)
        
        # 操作按钮区域
        buttons_frame = ttk.Frame(settings_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 全部启动按钮
        self.start_button = ttk.Button(buttons_frame, text="全部启动", command=self.start_all_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=10)
        
        # 全部停止按钮
        self.stop_button = ttk.Button(buttons_frame, text="全部停止", command=self.stop_all_monitoring, state=tk.NORMAL)
        self.stop_button.pack(side=tk.LEFT, padx=10)
        
        # 清空日志按钮
        ttk.Button(buttons_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=10)
        
        # 导出日志按钮
        ttk.Button(buttons_frame, text="导出日志", command=self.save_log).pack(side=tk.LEFT, padx=10)
        
        # 保存配置按钮
        ttk.Button(buttons_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=10)
        
        # 路径监控设置
        paths_frame = ttk.LabelFrame(settings_frame, text="监控路径")
        paths_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 路径列表框
        self.paths_listbox_frame = ttk.LabelFrame(paths_frame, text="已添加路径")
        self.paths_listbox_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建滚动条
        scrollbar = ttk.Scrollbar(self.paths_listbox_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建路径列表框
        self.paths_listbox = tk.Listbox(self.paths_listbox_frame, height=4, width=40, yscrollcommand=scrollbar.set)
        self.paths_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.config(command=self.paths_listbox.yview)
        
        # 路径操作按钮框架
        path_buttons_frame = ttk.Frame(paths_frame)
        path_buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 添加路径按钮
        ttk.Button(path_buttons_frame, text="添加路径", command=self.add_path).pack(side=tk.LEFT, padx=5)
        
        # 删除路径按钮
        ttk.Button(path_buttons_frame, text="删除路径", command=self.remove_path).pack(side=tk.LEFT, padx=5)
        
        # 清空路径按钮
        ttk.Button(path_buttons_frame, text="清空路径", command=self.clear_paths).pack(side=tk.LEFT, padx=5)
        
        # 排除路径设置
        excluded_paths_frame = ttk.LabelFrame(settings_frame, text="排除路径")
        excluded_paths_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 排除路径列表框
        self.excluded_paths_listbox_frame = ttk.LabelFrame(excluded_paths_frame, text="已排除路径")
        self.excluded_paths_listbox_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建滚动条
        excluded_scrollbar = ttk.Scrollbar(self.excluded_paths_listbox_frame)
        excluded_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建排除路径列表框
        self.excluded_paths_listbox = tk.Listbox(self.excluded_paths_listbox_frame, height=4, width=40, yscrollcommand=excluded_scrollbar.set)
        self.excluded_paths_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        excluded_scrollbar.config(command=self.excluded_paths_listbox.yview)
        
        # 排除路径操作按钮框架
        excluded_path_buttons_frame = ttk.Frame(excluded_paths_frame)
        excluded_path_buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 添加排除路径按钮
        ttk.Button(excluded_path_buttons_frame, text="添加排除", command=self.add_excluded_path).pack(side=tk.LEFT, padx=5)
        
        # 删除排除路径按钮
        ttk.Button(excluded_path_buttons_frame, text="删除排除", command=self.remove_excluded_path).pack(side=tk.LEFT, padx=5)
        
        # 清空排除路径按钮
        ttk.Button(excluded_path_buttons_frame, text="清空排除", command=self.clear_excluded_paths).pack(side=tk.LEFT, padx=5)
        
        # 监控间隔设置
        interval_frame = ttk.Frame(settings_frame)
        interval_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(interval_frame, text="监控检查间隔(秒):").pack(side=tk.LEFT, padx=5)
        
        # 间隔输入框
        self.interval_var = tk.StringVar(value="5")
        ttk.Entry(interval_frame, textvariable=self.interval_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # 日志文件设置
        log_file_frame = ttk.Frame(settings_frame)
        log_file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(log_file_frame, text="日志文件:").pack(side=tk.LEFT, padx=5)
        
        # 日志文件路径
        self.log_file_var = tk.StringVar(value="file_monitor.log")
        ttk.Entry(log_file_frame, textvariable=self.log_file_var, width=30).pack(side=tk.LEFT, padx=5)
        
        # 浏览按钮
        ttk.Button(log_file_frame, text="浏览", command=self.browse_log_file).pack(side=tk.LEFT, padx=5)
    
    def _setup_log_tab(self):
        # 创建日志框架
        log_frame = ttk.Frame(self.log_tab)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, width=80, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.configure(state='disabled')
        
        # 创建按钮框架
        button_frame = ttk.Frame(log_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 保存日志按钮
        ttk.Button(button_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=10)
        
        # 清空日志按钮
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=10)
        
        # 过滤器框架
        filter_frame = ttk.LabelFrame(log_frame, text="日志过滤")
        filter_frame.pack(fill=tk.X, pady=10)
        
        # 过滤类型
        ttk.Label(filter_frame, text="过滤类型:").grid(row=0, column=0, padx=5, pady=5)
        
        # 过滤类型下拉框
        self.filter_type = tk.StringVar(value="全部")
        filter_combo = ttk.Combobox(filter_frame, textvariable=self.filter_type, 
                                   values=["全部", "文件", "注册表", "服务", "临时文件"])
        filter_combo.grid(row=0, column=1, padx=5, pady=5)
        filter_combo.bind("<<ComboboxSelected>>", self.apply_filter)
        
        # 搜索框架
        search_frame = ttk.Frame(filter_frame)
        search_frame.grid(row=0, column=2, padx=20, pady=5)
        
        # 搜索输入框
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=5)
        self.search_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=self.search_var, width=20).pack(side=tk.LEFT, padx=5)
        
        # 搜索按钮
        ttk.Button(search_frame, text="搜索", command=self.search_log).pack(side=tk.LEFT, padx=5)
    
    # _setup_control_tab方法已移除，因为监控控制选项卡已被移除
    
    def _setup_about_tab(self):
        # 创建关于框架
        about_frame = ttk.Frame(self.about_tab)
        about_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        ttk.Label(about_frame, text="文件监控系统", font=("Arial", 16, "bold")).pack(pady=10)
        
        # 版本信息
        ttk.Label(about_frame, text="版本: 1.0.0").pack(pady=5)
        
        # 功能描述
        desc_frame = ttk.LabelFrame(about_frame, text="功能描述")
        desc_frame.pack(fill=tk.X, pady=10, padx=10)
        
        description = """这是一个使用Python开发的电脑文件监控软件，能够监控以下内容：

- 注册表变化（特别是开机启动项）
- Windows服务状态变化
- 指定盘符下的文件变化（创建、修改、删除、移动）
- 临时文件的变化

所有监控记录都会保存到日志文件中，方便随时查看系统的变化情况。"""
        
        desc_text = scrolledtext.ScrolledText(desc_frame, wrap=tk.WORD, width=60, height=10)
        desc_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        desc_text.insert(tk.END, description)
        desc_text.configure(state='disabled')
        
        # 使用说明
        usage_frame = ttk.LabelFrame(about_frame, text="使用说明")
        usage_frame.pack(fill=tk.X, pady=10, padx=10)
        
        usage = """1. 在"监控设置"选项卡中选择要监控的项目和路径
2. 点击"全部启动"按钮开始监控
3. 在"监控日志"选项卡中查看监控结果
4. 可以使用过滤和搜索功能查找特定的日志记录
5. 点击"全部停止"按钮停止监控
6. 可以随时保存或清空日志
7. 点击"保存配置"按钮将当前设置保存到config.ini文件"""
        
        usage_text = scrolledtext.ScrolledText(usage_frame, wrap=tk.WORD, width=60, height=8)
        usage_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        usage_text.insert(tk.END, usage)
        usage_text.configure(state='disabled')
        
        # 版权信息
        ttk.Label(about_frame, text="© 2023 文件监控系统").pack(pady=10)
    
    def _setup_logging(self):
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=[
                logging.FileHandler(self.log_file_var.get()),
                logging.StreamHandler()
            ]
        )
        
        # 添加GUI日志处理器
        gui_handler = GUILogHandler(self.log_text)
        logging.getLogger().addHandler(gui_handler)
    
    def add_path(self):
        # 添加监控路径
        path = filedialog.askdirectory(title="选择要监控的文件夹")
        if path and path not in self.monitor_paths:
            self.monitor_paths.append(path)
            self.paths_listbox.insert(tk.END, path)
    
    def remove_path(self):
        # 删除选中的监控路径
        selected = self.paths_listbox.curselection()
        if selected:
            index = selected[0]
            path = self.paths_listbox.get(index)
            self.monitor_paths.remove(path)
            self.paths_listbox.delete(index)
    
    def clear_paths(self):
        # 清空所有监控路径
        self.monitor_paths.clear()
        self.paths_listbox.delete(0, tk.END)
        
    def add_excluded_path(self):
        # 添加排除路径
        path = filedialog.askdirectory(title="选择要排除的文件夹")
        if path and path not in self.excluded_paths:
            self.excluded_paths.append(path)
            self.excluded_paths_listbox.insert(tk.END, path)
    
    def remove_excluded_path(self):
        # 删除选中的排除路径
        selected = self.excluded_paths_listbox.curselection()
        if selected:
            index = selected[0]
            path = self.excluded_paths_listbox.get(index)
            self.excluded_paths.remove(path)
            self.excluded_paths_listbox.delete(index)
    
    def clear_excluded_paths(self):
        # 清空所有排除路径
        self.excluded_paths.clear()
        self.excluded_paths_listbox.delete(0, tk.END)
    
    def browse_log_file(self):
        filename = filedialog.asksaveasfilename(
            defaultextension=".log",
            filetypes=[("Log files", "*.log"), ("All files", "*.*")],
            initialfile="file_monitor.log"
        )
        if filename:
            self.log_file_var.set(filename)
    
    def start_monitoring(self):
        # 检查是否选择了至少一个监控项
        if not (self.file_var.get() and self.monitor_paths) and not self.registry_var.get() and not self.service_var.get() and not self.temp_file_var.get():
            messagebox.showerror("错误", "请至少选择一项监控内容！")
            return
        
        # 如果选择了文件系统监控但没有添加路径，提示用户
        if self.file_var.get() and not self.monitor_paths:
            messagebox.showerror("错误", "已选择文件系统监控，但未添加任何监控路径！")
            return
        
        # 检查监控间隔是否有效
        try:
            interval = int(self.interval_var.get())
            if interval <= 0:
                raise ValueError("监控间隔必须大于0")
        except ValueError as e:
            messagebox.showerror("错误", f"无效的监控间隔: {str(e)}")
            return
        
        # 重新配置日志
        for handler in logging.getLogger().handlers[::]:
            if isinstance(handler, logging.FileHandler):
                logging.getLogger().removeHandler(handler)
        
        file_handler = logging.FileHandler(self.log_file_var.get())
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s', '%Y-%m-%d %H:%M:%S'))
        logging.getLogger().addHandler(file_handler)
        
        # 更新UI状态 - 确保在主线程中执行
        def update_ui():
            self.start_button.configure(state=tk.DISABLED)
            self.stop_button.configure(state=tk.NORMAL)
        
        # 使用after方法确保在主线程中更新UI
        self.root.after(0, update_ui)
        
        # 记录开始监控
        logging.info("=== 文件监控系统启动 ===")
        monitoring_items = []
        if self.registry_var.get():
            monitoring_items.append("注册表")
        if self.service_var.get():
            monitoring_items.append("服务")
        if self.file_var.get() and self.monitor_paths:
            monitoring_items.append("文件系统")
        if self.temp_file_var.get():
            monitoring_items.append("临时文件")
            
        logging.info(f"监控内容: {'、'.join(monitoring_items)}")
        if self.file_var.get() and self.monitor_paths:
            logging.info(f"监控路径: {', '.join(self.monitor_paths)}")
        if self.excluded_paths:
            logging.info(f"排除路径: {', '.join(self.excluded_paths)}")
        logging.info(f"日志文件: {self.log_file_var.get()}")
        
        # 重置停止事件
        self.stop_event.clear()
        
        # 创建并启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitoring_task, args=(self.monitor_paths, interval))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self.is_monitoring = True
    
    def monitoring_task(self, paths_to_monitor, interval):
        # 创建文件监控器
        self.observers = []
        if self.file_var.get() and paths_to_monitor:
            for path in paths_to_monitor:
                try:
                    event_handler = FileChangeHandler(excluded_paths=self.excluded_paths)
                    observer = Observer()
                    observer.schedule(event_handler, path, recursive=True)
                    observer.start()
                    self.observers.append(observer)
                    logging.info(f"开始监控路径: {path}")
                except Exception as e:
                    logging.error(f"启动路径 {path} 监控失败: {e}")
        
        # 创建注册表监控器
        self.registry_monitors = []
        if self.registry_var.get():
            registry_paths = [
                (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"),
                (winreg.HKEY_CURRENT_USER, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run")
            ]
            self.registry_monitors = [RegistryMonitor(hive, subkey) for hive, subkey in registry_paths]
            logging.info("开始监控注册表启动项")
        
        # 创建服务监控器
        self.service_monitor = None
        if self.service_var.get():
            self.service_monitor = ServiceMonitor()
            logging.info("开始监控Windows服务变化")
        
        # 创建临时文件监控器
        self.temp_file_monitor = None
        if self.temp_file_var.get():
            self.temp_file_monitor = TempFileMonitor()
            logging.info("开始监控系统临时文件夹")
        
        # 监控循环
        while not self.stop_event.is_set():
            try:
                # 检查注册表变化
                if self.registry_var.get() and self.registry_monitors:
                    for monitor in self.registry_monitors:
                        monitor.check_changes()
                
                # 检查服务变化
                if self.service_var.get() and self.service_monitor:
                    self.service_monitor.check_changes()
                
                # 检查临时文件变化
                if self.temp_file_var.get() and self.temp_file_monitor:
                    self.temp_file_monitor.check_changes()
                
                # 等待指定的间隔时间
                for _ in range(interval):
                    if self.stop_event.is_set():
                        break
                    time.sleep(1)
            except Exception as e:
                logging.error(f"监控过程中发生错误: {e}")
                time.sleep(interval)
    
    def stop_monitoring(self):
        if not self.is_monitoring:
            return

        # 设置停止事件
        self.stop_event.set()

        # 停止所有观察者
        for observer in self.observers:
            try:
                if hasattr(observer, 'stop'):
                    observer.stop()
            except Exception:
                # 忽略停止时的错误，这可能是Python 3.13兼容性问题
                pass

        # 等待观察者线程结束
        for observer in self.observers:
            try:
                if hasattr(observer, 'join'):
                    observer.join(timeout=0.5)  # 减少等待时间
            except Exception:
                # 忽略join时的错误
                pass

        # 等待监控线程结束
        if self.monitor_thread and hasattr(self.monitor_thread, 'join'):
            try:
                self.monitor_thread.join(timeout=1.0)  # 最多等待1秒
            except Exception:
                # 忽略线程join时的错误
                pass

        # 清空监控对象
        self.observers = []
        self.registry_monitors = []
        self.service_monitor = None
        self.temp_file_monitor = None
        
        # 更新UI状态 - 确保在主线程中执行
        def update_ui():
            self.start_button.configure(state=tk.NORMAL)
            self.stop_button.configure(state=tk.DISABLED)
            # 记录停止监控
            logging.info("=== 文件监控系统停止 ===")
            self.is_monitoring = False
        
        # 使用after方法确保在主线程中更新UI
        self.root.after(0, update_ui)
    
    def clear_log(self):
        # 清空日志文本框
        self.log_text.configure(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.configure(state='disabled')

        # 记录日志
        logging.info("=== 日志已清空 ===")
    
    def save_log(self):
        # 保存日志到文件
        filename = filedialog.asksaveasfilename(
            defaultextension=".log",
            filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")],
            initialfile="file_monitor_export.log"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    # 写入日志文本框内容
                    f.write("=== 监控日志 ===\n\n")
                    f.write(self.log_text.get(1.0, tk.END))

                messagebox.showinfo("成功", f"日志已保存到 {filename}")
                # 记录日志
                logging.info(f"日志已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {str(e)}")
    
    def apply_filter(self, event=None):
        # 应用过滤器
        filter_type = self.filter_type.get()
        
        # 获取所有日志
        self.log_text.configure(state='normal')
        all_log = self.log_text.get(1.0, tk.END)
        self.log_text.delete(1.0, tk.END)
        
        # 根据过滤类型筛选日志
        for line in all_log.split('\n'):
            if filter_type == "全部" or \
               (filter_type == "文件" and ("文件创建" in line or "文件删除" in line or "文件修改" in line or "文件移动" in line)) or \
               (filter_type == "注册表" and "注册表" in line) or \
               (filter_type == "服务" and "服务" in line) or \
               (filter_type == "临时文件" and "临时文件" in line):
                self.log_text.insert(tk.END, line + '\n')
        
        self.log_text.configure(state='disabled')
    
    def search_log(self):
        # 搜索日志
        search_text = self.search_var.get().lower()
        if not search_text:
            return
        
        # 获取所有日志
        self.log_text.configure(state='normal')
        all_log = self.log_text.get(1.0, tk.END)
        self.log_text.delete(1.0, tk.END)
        
        # 搜索并高亮匹配的行
        for line in all_log.split('\n'):
            if search_text in line.lower():
                self.log_text.insert(tk.END, line + '\n')
        
        self.log_text.configure(state='disabled')
    
    def start_all_monitoring(self):
        # 启动监控
        self.start_monitoring()
    
    def stop_all_monitoring(self):
        # 停止所有监控
        self.stop_monitoring()
    
    # on_tab_changed方法已移除，因为监控控制选项卡已被移除
    
    def save_config(self):
        """保存配置到config.ini文件"""
        config = configparser.ConfigParser()
        
        # 监控设置
        config['Monitor'] = {
            'registry': str(self.registry_var.get()),
            'service': str(self.service_var.get()),
            'file': str(self.file_var.get()),
            'temp_file': str(self.temp_file_var.get()),
            'interval': self.interval_var.get(),
            'log_file': self.log_file_var.get()
        }
        
        # 监控路径
        paths = []
        for i in range(self.paths_listbox.size()):
            paths.append(self.paths_listbox.get(i))
        
        config['Paths'] = {
            'count': str(len(paths))
        }
        
        for i, path in enumerate(paths):
            config['Paths'][f'path{i}'] = path
        
        # 排除路径
        excluded_paths = []
        for i in range(self.excluded_paths_listbox.size()):
            excluded_paths.append(self.excluded_paths_listbox.get(i))
        
        config['ExcludedPaths'] = {
            'count': str(len(excluded_paths))
        }
        
        for i, path in enumerate(excluded_paths):
            config['ExcludedPaths'][f'path{i}'] = path
        
        # 保存配置
        with open(self.config_file, 'w') as f:
            config.write(f)
        
        messagebox.showinfo("配置保存", "配置已成功保存到 config.ini 文件")
    
    def load_config(self):
        """从config.ini文件加载配置"""
        if not os.path.exists(self.config_file):
            return
        
        config = configparser.ConfigParser()
        config.read(self.config_file)
        
        # 加载监控设置
        if 'Monitor' in config:
            if 'registry' in config['Monitor']:
                self.registry_var.set(config['Monitor']['registry'].lower() == 'true')
            if 'service' in config['Monitor']:
                self.service_var.set(config['Monitor']['service'].lower() == 'true')
            if 'file' in config['Monitor']:
                self.file_var.set(config['Monitor']['file'].lower() == 'true')
            if 'temp_file' in config['Monitor']:
                self.temp_file_var.set(config['Monitor']['temp_file'].lower() == 'true')
            if 'interval' in config['Monitor']:
                self.interval_var.set(config['Monitor']['interval'])
            if 'log_file' in config['Monitor']:
                self.log_file_var.set(config['Monitor']['log_file'])
        
        # 加载监控路径
        if 'Paths' in config and 'count' in config['Paths']:
            self.paths_listbox.delete(0, tk.END)
            count = int(config['Paths']['count'])
            for i in range(count):
                path_key = f'path{i}'
                if path_key in config['Paths']:
                    self.paths_listbox.insert(tk.END, config['Paths'][path_key])
                    self.monitor_paths.append(config['Paths'][path_key])
        
        # 加载排除路径
        if 'ExcludedPaths' in config and 'count' in config['ExcludedPaths']:
            self.excluded_paths_listbox.delete(0, tk.END)
            count = int(config['ExcludedPaths']['count'])
            for i in range(count):
                path_key = f'path{i}'
                if path_key in config['ExcludedPaths']:
                    self.excluded_paths_listbox.insert(tk.END, config['ExcludedPaths'][path_key])
                    self.excluded_paths.append(config['ExcludedPaths'][path_key])
    
    def on_closing(self):
        # 关闭窗口时的处理
        if self.is_monitoring:
            if messagebox.askyesno("确认", "监控正在进行中，确定要退出吗？"):
                # 停止监控
                self.stop_event.set()
                
                # 停止所有观察者
                for observer in self.observers:
                    try:
                        if hasattr(observer, 'stop'):
                            observer.stop()
                    except Exception as e:
                        logging.error(f"停止观察者时出错: {e}")
                
                # 确保按钮状态正确
                self.start_button.configure(state=tk.NORMAL)
                self.stop_button.configure(state=tk.DISABLED)
                
                # 销毁窗口
                self.root.destroy()
        else:
            self.root.destroy()

def main():
    root = tk.Tk()
    app = FileMonitorApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()